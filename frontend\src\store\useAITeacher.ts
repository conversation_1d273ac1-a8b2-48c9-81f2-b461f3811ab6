// Zustand store for AI Teacher Avatar state management

import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { TeacherAvatar, Viseme, AITeacherStore } from '@/types';

export const useAITeacher = create<AITeacherStore>()(
  devtools(
    (set) => ({
      // State
      avatar: undefined,
      isModelLoaded: false,
      isSpeaking: false,
      currentVisemes: [],
      blackboardText: '',

      // Actions
      setAvatar: (avatar: TeacherAvatar) => {
        set({ avatar }, false, 'setAvatar');
      },

      setModelLoaded: (loaded: boolean) => {
        set({ isModelLoaded: loaded }, false, 'setModelLoaded');
      },

      setSpeaking: (speaking: boolean) => {
        set({ isSpeaking: speaking }, false, 'setSpeaking');
        
        // Clear visemes when not speaking
        if (!speaking) {
          set({ currentVisemes: [] }, false, 'clearVisemes');
        }
      },

      setCurrentVisemes: (visemes: Viseme[]) => {
        set({ currentVisemes: visemes }, false, 'setCurrentVisemes');
      },

      setBlackboardText: (text: string) => {
        set({ blackboardText: text }, false, 'setBlackboardText');
      },
    }),
    {
      name: 'ai-teacher-store',
    }
  )
);

// Selectors for optimized re-renders
export const useAvatarConfig = () => useAITeacher((state) => state.avatar);
export const useModelLoaded = () => useAITeacher((state) => state.isModelLoaded);
export const useIsSpeaking = () => useAITeacher((state) => state.isSpeaking);
export const useCurrentVisemes = () => useAITeacher((state) => state.currentVisemes);
export const useBlackboardText = () => useAITeacher((state) => state.blackboardText);

// Actions selectors
export const useAITeacherActions = () => useAITeacher((state) => ({
  setAvatar: state.setAvatar,
  setModelLoaded: state.setModelLoaded,
  setSpeaking: state.setSpeaking,
  setCurrentVisemes: state.setCurrentVisemes,
  setBlackboardText: state.setBlackboardText,
}));

// Helper functions for avatar management
export const useAvatarHelpers = () => {
  const { avatar, setAvatar } = useAITeacher();

  const updateAvatarAppearance = (appearance: Partial<TeacherAvatar['appearance']>) => {
    if (avatar) {
      setAvatar({
        ...avatar,
        appearance: { ...avatar.appearance, ...appearance },
        updatedAt: new Date(),
      });
    }
  };

  const updateVoiceSettings = (voiceSettings: Partial<TeacherAvatar['voiceSettings']>) => {
    if (avatar) {
      setAvatar({
        ...avatar,
        voiceSettings: { ...avatar.voiceSettings, ...voiceSettings },
        updatedAt: new Date(),
      });
    }
  };

  const updatePersonality = (personality: Partial<TeacherAvatar['personality']>) => {
    if (avatar) {
      setAvatar({
        ...avatar,
        personality: { ...avatar.personality, ...personality },
        updatedAt: new Date(),
      });
    }
  };

  return {
    updateAvatarAppearance,
    updateVoiceSettings,
    updatePersonality,
  };
};

// Animation helpers for lip-sync
export const useVisemeAnimation = () => {
  const { currentVisemes, setCurrentVisemes, setSpeaking } = useAITeacher();

  const playVisemeSequence = (visemes: Viseme[], audioElement?: HTMLAudioElement) => {
    if (visemes.length === 0) return;

    setSpeaking(true);
    setCurrentVisemes(visemes);

    // If audio element is provided, sync with audio playback
    if (audioElement) {
      const handleAudioEnd = () => {
        setSpeaking(false);
        setCurrentVisemes([]);
        audioElement.removeEventListener('ended', handleAudioEnd);
      };

      audioElement.addEventListener('ended', handleAudioEnd);
      audioElement.play().catch(console.error);
    } else {
      // Fallback: estimate duration based on viseme timestamps
      const duration = visemes[visemes.length - 1]?.time || 3000;
      setTimeout(() => {
        setSpeaking(false);
        setCurrentVisemes([]);
      }, duration);
    }
  };

  const stopVisemeSequence = () => {
    setSpeaking(false);
    setCurrentVisemes([]);
  };

  return {
    playVisemeSequence,
    stopVisemeSequence,
    currentVisemes,
  };
};

// Default avatar configuration
export const defaultAvatarConfig: Omit<TeacherAvatar, 'id' | 'teacherId' | 'createdAt' | 'updatedAt'> = {
  modelUrl: '/models/teacher-default.glb',
  voiceId: 'default-voice',
  voiceSettings: {
    pitch: 1.0,
    speed: 1.0,
    volume: 0.8,
  },
  appearance: {
    skinTone: 'medium',
    hairColor: 'brown',
    eyeColor: 'brown',
    clothing: 'professional',
  },
  personality: {
    tone: 'friendly',
    enthusiasm: 7,
    patience: 8,
  },
};

// Persistence helpers (for saving to backend)
export const useAvatarPersistence = () => {
  const { avatar } = useAITeacher();

  const saveAvatarToBackend = async () => {
    if (!avatar) return false;

    try {
      // TODO: Implement actual API call when backend is ready
      // const response = await avatarApi.saveAvatar(avatar);
      console.log('Saving avatar configuration:', avatar);
      return true;
    } catch (error) {
      console.error('Failed to save avatar:', error);
      return false;
    }
  };

  const loadAvatarFromBackend = async (teacherId?: string) => {
    try {
      // TODO: Implement actual API call when backend is ready
      // const response = await avatarApi.getAvatar(teacherId);
      console.log('Loading avatar configuration for teacher:', teacherId);
      
      // For now, return default config
      return defaultAvatarConfig;
    } catch (error) {
      console.error('Failed to load avatar:', error);
      return null;
    }
  };

  return {
    saveAvatarToBackend,
    loadAvatarFromBackend,
  };
};
